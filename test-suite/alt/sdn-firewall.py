#!/usr/bin/python
# CS 6250 Fall 2024- SDN Firewall Project with POX
# build hackers-45

from pox.core import core
import pox.openflow.libopenflow_01 as of
import pox.lib.packet as pkt
from pox.lib.revent import *
from pox.lib.addresses import IPAddr, EthAddr

# You may use this space before the firewall_policy_processing function to add any extra function that you 
# may need to complete your firewall implementation.  No additional functions "should" be required to complete
# this assignment.


def firewall_policy_processing(policies):
    '''
    This is where you are to implement your code that will build POX/Openflow Match and Action operations to
    create a dynamic firewall meeting the requirements specified in your configure.pol file.  Do NOT hardcode
    the IP/MAC Addresses/Protocols/Ports that are specified in the project description - this code should use
    the values provided in the configure.pol to implement the firewall.

    The policies passed to this function is a list of dictionary objects that contain the data imported from the
    configure.pol file.  The policy variable in the "for policy in policies" represents a single line from the
    configure.pol file.  Each of the configuration values are then accessed using the policy['field'] command. 
    The fields are:  'rulenum','action','mac-src','mac-dst','ip-src','ip-dst','ipprotocol','port-src','port-dst',
    'comment'.

    Your return from this function is a list of flow_mods that represent the different rules in your configure.pol file.

    Implementation Hints:
    The documentation for the POX controller is available at https://noxrepo.github.io/pox-doc/html .  This project
    is using the gar-experimental branch of POX in order to properly support Python 3.  To complete this project, you
    need to use the OpenFlow match and flow_modification routines (https://noxrepo.github.io/pox-doc/html/#openflow-messages
    for flow_mod and https://noxrepo.github.io/pox-doc/html/#match-structure for match.)  Also, do NOT wrap IP Addresses with
    IPAddr() unless you reformat the CIDR notation.  Look at the https://github.com/att/pox/blob/master/pox/lib/addresses.py
    for what POX is expecting as an IP Address.
    '''

    rules = []

    # Priority variables - Allow rules have higher priority than Block rules
    allowPriority = 11_000  # Higher priority for Allow rules
    blockPriority = 0   # Lower priority for Block rules

    for policy in policies:
        # Create OpenFlow match object
        match = of.ofp_match()

        # Set MAC addresses if specified
        if policy['mac-src'] != '-':
            match.dl_src = EthAddr(policy['mac-src'])
        if policy['mac-dst'] != '-':
            match.dl_dst = EthAddr(policy['mac-dst'])

        # Set IP addresses if specified
        if policy['ip-src'] != '-':
            # Handle CIDR notation
            if '/' in policy['ip-src']:
                ip_parts = policy['ip-src'].split('/')
                match.nw_src = IPAddr(ip_parts[0])
                # Convert CIDR prefix to netmask
                prefix_len = int(ip_parts[1])
                if prefix_len == 32:
                    match.nw_src = IPAddr(ip_parts[0])
                else:
                    # Calculate network address and mask
                    import socket
                    import struct
                    mask = (0xffffffff >> (32 - prefix_len)) << (32 - prefix_len)
                    ip_int = struct.unpack("!I", socket.inet_aton(ip_parts[0]))[0]
                    network_int = ip_int & mask
                    network_ip = socket.inet_ntoa(struct.pack("!I", network_int))
                    match.nw_src = IPAddr(network_ip)
                    # Set wildcard bits (inverse of netmask)
                    match.wildcards &= ~of.OFPFW_NW_SRC_MASK
                    match.wildcards |= (32 - prefix_len) << of.OFPFW_NW_SRC_SHIFT
            else:
                match.nw_src = IPAddr(policy['ip-src'])

        if policy['ip-dst'] != '-':
            # Handle CIDR notation
            if '/' in policy['ip-dst']:
                ip_parts = policy['ip-dst'].split('/')
                match.nw_dst = IPAddr(ip_parts[0])
                # Convert CIDR prefix to netmask
                prefix_len = int(ip_parts[1])
                if prefix_len == 32:
                    match.nw_dst = IPAddr(ip_parts[0])
                else:
                    # Calculate network address and mask
                    import socket
                    import struct
                    mask = (0xffffffff >> (32 - prefix_len)) << (32 - prefix_len)
                    ip_int = struct.unpack("!I", socket.inet_aton(ip_parts[0]))[0]
                    network_int = ip_int & mask
                    network_ip = socket.inet_ntoa(struct.pack("!I", network_int))
                    match.nw_dst = IPAddr(network_ip)
                    # Set wildcard bits (inverse of netmask)
                    match.wildcards &= ~of.OFPFW_NW_DST_MASK
                    match.wildcards |= (32 - prefix_len) << of.OFPFW_NW_DST_SHIFT
            else:
                match.nw_dst = IPAddr(policy['ip-dst'])

        # Set IP protocol if specified
        if policy['ipprotocol'] != '-':
            match.nw_proto = int(policy['ipprotocol'])
            match.dl_type = 0x0800  # IPv4 EtherType

        # Set transport layer ports if specified
        if policy['port-src'] != '-':
            match.tp_src = int(policy['port-src'])
        if policy['port-dst'] != '-':
            match.tp_dst = int(policy['port-dst'])

        # Create flow modification message
        rule = of.ofp_flow_mod()
        rule.match = match

        # Set priority based on action type
        if policy['action'].lower() == 'allow':
            rule.priority = allowPriority
            # Allow rules use OFPP_CONTROLLER action to forward to controller
            action = of.ofp_action_output(port=of.OFPP_CONTROLLER)
            rule.actions.append(action)
        elif policy['action'].lower() == 'block':
            rule.priority = blockPriority
            # Block rules have no actions (drop packets)
            pass

        # Set flow mod parameters
        rule.command = of.OFPFC_ADD
        rule.idle_timeout = 0  # Permanent rule
        rule.hard_timeout = 0  # Permanent rule

        print('Added Rule ',policy['rulenum'],': ',policy['comment'])
        #print(rule)   #Uncomment this to debug your "rule"
        rules.append(rule)

    return rules
